[{"C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\App.js": "3", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Courses.js": "4", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Login.js": "5", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Signup.js": "6", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Dashboard.js": "7", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Home.js": "8", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\components\\Navigation.js": "9", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\CreateCourse.js": "10", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Profile.js": "11", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\AdminPanel.js": "12", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\StudentDetailsForm.js": "13", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\CourseContent.js": "14", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Quiz.js": "15", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\CourseCompletion.js": "16", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\config\\api.js": "17", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\EditCourse.js": "18", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Payment.js": "19", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\PaymentHistory.js": "20", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\PaymentAnalytics.js": "21", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\PaymentVerification.js": "22", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\LandingPage.js": "23", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\EditPDF.js": "24", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\ExploreMore.js": "25", "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\CreatePDF.js": "26"}, {"size": 535, "mtime": 1750237602000, "results": "27", "hashOfConfig": "28"}, {"size": 362, "mtime": 1750237602000, "results": "29", "hashOfConfig": "28"}, {"size": 2600, "mtime": 1753681922273, "results": "30", "hashOfConfig": "28"}, {"size": 9354, "mtime": 1752901999557, "results": "31", "hashOfConfig": "28"}, {"size": 4312, "mtime": 1753680902865, "results": "32", "hashOfConfig": "28"}, {"size": 4991, "mtime": 1753680627790, "results": "33", "hashOfConfig": "28"}, {"size": 10094, "mtime": 1753681564299, "results": "34", "hashOfConfig": "28"}, {"size": 4121, "mtime": 1752901902459, "results": "35", "hashOfConfig": "28"}, {"size": 4289, "mtime": 1753680639059, "results": "36", "hashOfConfig": "28"}, {"size": 18593, "mtime": 1752902001113, "results": "37", "hashOfConfig": "28"}, {"size": 5923, "mtime": 1750253266000, "results": "38", "hashOfConfig": "28"}, {"size": 25299, "mtime": 1753684566840, "results": "39", "hashOfConfig": "28"}, {"size": 7490, "mtime": 1751018743998, "results": "40", "hashOfConfig": "28"}, {"size": 13979, "mtime": 1751035701648, "results": "41", "hashOfConfig": "28"}, {"size": 10088, "mtime": 1750259814000, "results": "42", "hashOfConfig": "28"}, {"size": 8940, "mtime": 1750253616000, "results": "43", "hashOfConfig": "28"}, {"size": 80, "mtime": 1750252500000, "results": "44", "hashOfConfig": "28"}, {"size": 19617, "mtime": 1751032905925, "results": "45", "hashOfConfig": "28"}, {"size": 23882, "mtime": 1752902002537, "results": "46", "hashOfConfig": "28"}, {"size": 7731, "mtime": 1752901999925, "results": "47", "hashOfConfig": "28"}, {"size": 8514, "mtime": 1752902000115, "results": "48", "hashOfConfig": "28"}, {"size": 9859, "mtime": 1751035749632, "results": "49", "hashOfConfig": "28"}, {"size": 7924, "mtime": 1753680725436, "results": "50", "hashOfConfig": "28"}, {"size": 12640, "mtime": 1753682406606, "results": "51", "hashOfConfig": "28"}, {"size": 11844, "mtime": 1753684556150, "results": "52", "hashOfConfig": "28"}, {"size": 9434, "mtime": 1753682332521, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dxtzl8", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Courses.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Signup.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Dashboard.js", ["132"], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\components\\Navigation.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\CreateCourse.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\AdminPanel.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\StudentDetailsForm.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\CourseContent.js", ["133"], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Quiz.js", ["134"], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\CourseCompletion.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\config\\api.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\EditCourse.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\Payment.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\PaymentHistory.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\PaymentAnalytics.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\PaymentVerification.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\LandingPage.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\EditPDF.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\ExploreMore.js", [], [], "C:\\Users\\<USER>\\Downloads\\lmsYproject\\frontend\\src\\pages\\CreatePDF.js", [], [], {"ruleId": "135", "severity": 1, "message": "136", "line": 9, "column": 10, "nodeType": "137", "messageId": "138", "endLine": 9, "endColumn": 25}, {"ruleId": "135", "severity": 1, "message": "139", "line": 10, "column": 10, "nodeType": "137", "messageId": "138", "endLine": 10, "endColumn": 18}, {"ruleId": "140", "severity": 1, "message": "141", "line": 43, "column": 6, "nodeType": "142", "endLine": 43, "endColumn": 29, "suggestions": "143"}, "no-unused-vars", "'enrolledCourses' is assigned a value but never used.", "Identifier", "unusedVar", "'progress' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleSubmitQuiz'. Either include it or remove the dependency array.", "ArrayExpression", ["144"], {"desc": "145", "fix": "146"}, "Update the dependencies array to be: [timeLeft, showResults, handleSubmitQuiz]", {"range": "147", "text": "148"}, [1345, 1368], "[time<PERSON><PERSON>t, showResults, handleSubmitQuiz]"]