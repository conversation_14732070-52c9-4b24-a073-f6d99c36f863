const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');
const https = require('https');

// Download a real PDF file from the internet
function downloadPDF(url, filename) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filename);
    https.get(url, (response) => {
      response.pipe(file);
      file.on('finish', () => {
        file.close();
        resolve();
      });
    }).on('error', (err) => {
      fs.unlink(filename, () => {}); // Delete the file on error
      reject(err);
    });
  });
}

// Create a minimal but valid PDF
function createMinimalPDF(title) {
  return `%PDF-1.4
1 0 obj
<< /Type /Catalog /Pages 2 0 R >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R /Resources << /Font << /F1 5 0 R >> >> >>
endobj
4 0 obj
<< /Length 73 >>
stream
BT
/F1 24 Tf
100 700 Td
(${title}) Tj
0 -50 Td
/F1 12 Tf
(This PDF should display correctly.) Tj
ET
endstream
endobj
5 0 obj
<< /Type /Font /Subtype /Type1 /BaseFont /Helvetica >>
endobj
xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000398 00000 n 
trailer
<< /Size 6 /Root 1 0 R >>
startxref
493
%%EOF`;
}

async function fixPDFDisplay() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db('LMS');
    const pdfsCollection = db.collection('pdfs');
    const usersCollection = db.collection('users');
    
    // Find admin user
    let admin = await usersCollection.findOne({ email: '<EMAIL>' });
    if (!admin) {
      console.log('❌ Admin user not found');
      return;
    }
    
    // Clear existing PDFs
    await pdfsCollection.deleteMany({});
    console.log('🗑️ Cleared existing PDF documents');
    
    // Create uploads directory
    const uploadsDir = path.join(__dirname, 'uploads/pdfs');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // Clear existing PDF files
    const existingFiles = fs.readdirSync(uploadsDir).filter(file => file.endsWith('.pdf'));
    existingFiles.forEach(file => {
      fs.unlinkSync(path.join(uploadsDir, file));
    });
    console.log('🗑️ Cleared existing PDF files');
    
    // Create simple test PDFs
    const testPDFs = [
      {
        title: 'Sample Document 1',
        description: 'This is a test PDF document that should display properly in your browser.',
        category: 'Study Material',
        subject: 'Testing'
      },
      {
        title: 'Sample Document 2', 
        description: 'Another test PDF to verify the display functionality works correctly.',
        category: 'Academic',
        subject: 'Testing'
      }
    ];
    
    for (let i = 0; i < testPDFs.length; i++) {
      const pdfData = testPDFs[i];
      const fileName = `test-pdf-${Date.now()}-${i}.pdf`;
      const filePath = path.join(uploadsDir, fileName);
      
      // Create minimal PDF
      const pdfContent = createMinimalPDF(pdfData.title);
      fs.writeFileSync(filePath, pdfContent, 'binary');
      
      console.log(`📄 Created PDF file: ${fileName}`);
      console.log(`📁 File path: ${filePath}`);
      console.log(`📊 File size: ${fs.statSync(filePath).size} bytes`);
      
      // Add to database
      const pdfRecord = {
        title: pdfData.title,
        description: pdfData.description,
        category: pdfData.category,
        subject: pdfData.subject,
        pdfUrl: `/uploads/pdfs/${fileName}`,
        fileName: fileName,
        originalName: `${pdfData.title.toLowerCase().replace(/\s+/g, '-')}.pdf`,
        fileSize: (fs.statSync(filePath).size / 1024).toFixed(2) + ' KB',
        fileSizeBytes: fs.statSync(filePath).size,
        mimeType: 'application/pdf',
        pages: 1,
        tags: ['test', 'sample'],
        downloadCount: 0,
        viewCount: 0,
        isActive: true,
        uploadedBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await pdfsCollection.insertOne(pdfRecord);
      console.log(`✅ Added to database: ${pdfData.title}`);
      
      // Test the PDF file directly
      console.log(`🔗 Direct URL: http://localhost:5002/uploads/pdfs/${fileName}`);
    }
    
    console.log('\n🎯 TESTING INSTRUCTIONS:');
    console.log('1. Open the direct URLs above in your browser');
    console.log('2. If PDFs don\'t display, the issue is with PDF content');
    console.log('3. If PDFs display directly but not in the app, the issue is with the frontend');
    console.log('4. Check browser console for any JavaScript errors');
    
    console.log('\n📋 Next steps:');
    console.log('- Test direct PDF URLs first');
    console.log('- Then test: http://localhost:3001/LearnX/explore-more');
    console.log('- Check browser developer tools for errors');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
  }
}

fixPDFDisplay();
