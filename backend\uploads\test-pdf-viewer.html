<!DOCTYPE html>
<html>
<head>
    <title>PDF Display Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        iframe { border: 1px solid #000; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>PDF Display Diagnostic Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Direct PDF Link</h2>
        <p>Click this link to open PDF in new tab:</p>
        <a href="http://localhost:5002/uploads/pdfs/test-pdf-1753687290177-0.pdf" target="_blank">
            Open PDF in New Tab
        </a>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Embedded PDF (iframe)</h2>
        <p>PDF embedded in iframe:</p>
        <iframe 
            src="http://localhost:5002/uploads/pdfs/test-pdf-1753687290177-0.pdf" 
            width="600" 
            height="400">
            <p class="error">Your browser does not support iframes or PDF viewing.</p>
        </iframe>
    </div>
    
    <div class="test-section">
        <h2>Test 3: PDF Object Embed</h2>
        <p>PDF embedded using object tag:</p>
        <object 
            data="http://localhost:5002/uploads/pdfs/test-pdf-1753687290177-0.pdf" 
            type="application/pdf" 
            width="600" 
            height="400">
            <p class="error">Your browser cannot display PDF files. 
            <a href="http://localhost:5002/uploads/pdfs/test-pdf-1753687290177-0.pdf">Download the PDF</a>
            </p>
        </object>
    </div>
    
    <div class="test-section">
        <h2>Test 4: PDF Embed Tag</h2>
        <p>PDF embedded using embed tag:</p>
        <embed 
            src="http://localhost:5002/uploads/pdfs/test-pdf-1753687290177-0.pdf" 
            type="application/pdf" 
            width="600" 
            height="400">
    </div>
    
    <div class="test-section">
        <h2>Browser Information</h2>
        <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
        <p><strong>PDF Plugin Available:</strong> <span id="pdfPlugin"></span></p>
        <p><strong>Current Time:</strong> <span id="currentTime"></span></p>
    </div>
    
    <script>
        // Display browser info
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('currentTime').textContent = new Date().toLocaleString();
        
        // Check for PDF plugin
        const hasPDFPlugin = navigator.mimeTypes['application/pdf'] || 
                           navigator.plugins['Chrome PDF Plugin'] || 
                           navigator.plugins['Adobe Acrobat'];
        document.getElementById('pdfPlugin').textContent = hasPDFPlugin ? 'Yes' : 'No';
        document.getElementById('pdfPlugin').className = hasPDFPlugin ? 'success' : 'error';
        
        // Log any errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
        });
        
        console.log('PDF Test Page Loaded');
        console.log('Testing PDF URL: http://localhost:5002/uploads/pdfs/test-pdf-1753687290177-0.pdf');
    </script>
</body>
</html>
